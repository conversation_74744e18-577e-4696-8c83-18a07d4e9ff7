<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Signals ISN Service</title>
  <style>
    body {
      font-family: sans-serif;
      background-color: #f9fafb;
      color: #111827;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      margin: 0;
    }
    .logo {
      width: 100px;
      height: 100px;
      margin-bottom: 20px;
    }
    h1 {
      font-size: 1.8rem;
      margin-bottom: 1rem;
    }
    a {
      font-size: 1rem;
      color: #3b82f6;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    .traffic-light {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 20px 0;
      padding: 15px;
      background: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .status-indicator {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin: 5px;
      transition: all 0.3s ease;
    }
    .status-indicator.green {
      background-color: #10b981;
      box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
    }
    .status-indicator.red {
      background-color: #ef4444;
      box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
    }
    .status-indicator.gray {
      background-color: #6b7280;
    }
    .status-text {
      font-size: 0.875rem;
      margin-top: 8px;
      text-align: center;
    }
    .status-text.healthy {
      color: #10b981;
    }
    .status-text.unhealthy {
      color: #ef4444;
    }
    .status-text.checking {
      color: #6b7280;
    }
  </style>
</head>
<body>
    <img src="/assets/signals.svg">

    <div class="traffic-light">
      <div class="status-indicator gray" id="status-light"></div>
      <div class="status-text checking" id="status-text">Checking service status...</div>
    </div>

  <a href="/docs">Go to the service admin API documentation</a>

  <script>
    async function checkServiceStatus() {
      const statusLight = document.getElementById('status-light');
      const statusText = document.getElementById('status-text');

      try {
        const response = await fetch('/health/ready');
        if (response.ok) {
          statusLight.className = 'status-indicator green';
          statusText.className = 'status-text healthy';
          statusText.textContent = 'Service is healthy';
        } else {
          statusLight.className = 'status-indicator red';
          statusText.className = 'status-text unhealthy';
          statusText.textContent = 'Service is unhealthy';
        }
      } catch (error) {
        statusLight.className = 'status-indicator red';
        statusText.className = 'status-text unhealthy';
        statusText.textContent = 'Service is unavailable';
      }
    }

    // Check status immediately and then every 30 seconds
    checkServiceStatus();
    setInterval(checkServiceStatus, 30000);
  </script>
</body>
</html>


